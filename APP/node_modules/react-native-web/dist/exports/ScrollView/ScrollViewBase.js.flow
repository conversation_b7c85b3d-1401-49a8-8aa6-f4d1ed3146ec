/**
 * Copyright (c) <PERSON>.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

import type { ViewProps } from '../View';
import * as React from 'react';
import StyleSheet from '../StyleSheet';
import View from '../View';
import useMergeRefs from '../../modules/useMergeRefs';
type Props = {
  ...ViewProps,
  onMomentumScrollBegin?: (e: any) => void,
  onMomentumScrollEnd?: (e: any) => void,
  onScroll?: (e: any) => void,
  onScrollBeginDrag?: (e: any) => void,
  onScrollEndDrag?: (e: any) => void,
  onTouchMove?: (e: any) => void,
  onWheel?: (e: any) => void,
  scrollEnabled?: boolean,
  scrollEventThrottle?: number,
  showsHorizontalScrollIndicator?: boolean,
  showsVerticalScrollIndicator?: boolean,
};
declare function normalizeScrollEvent(e: any): any;
declare function shouldEmitScrollEvent(lastTick: number, eventThrottle: number): any;
/**
 * Encapsulates the Web-specific scroll throttling and disabling logic
 */
const ScrollViewBase: React.AbstractComponent<Props, React.ElementRef<typeof View>> = React.forwardRef((props, forwardedRef) => {
  const {
    onScroll,
    onTouchMove,
    onWheel,
    scrollEnabled = true,
    scrollEventThrottle = 0,
    showsHorizontalScrollIndicator,
    showsVerticalScrollIndicator,
    style,
    ...rest
  } = props;
  const scrollState = React.useRef({
    isScrolling: false,
    scrollLastTick: 0
  });
  const scrollTimeout = React.useRef(null);
  const scrollRef = React.useRef(null);
  declare function createPreventableScrollHandler(handler: Function): any;
  declare function handleScroll(e: Object): any;
  declare function handleScrollStart(e: Object): any;
  declare function handleScrollTick(e: Object): any;
  declare function handleScrollEnd(e: Object): any;
  const hideScrollbar = showsHorizontalScrollIndicator === false || showsVerticalScrollIndicator === false;
  return <View {...rest} onScroll={handleScroll} onTouchMove={createPreventableScrollHandler(onTouchMove)} onWheel={createPreventableScrollHandler(onWheel)} ref={useMergeRefs(scrollRef, forwardedRef)} style={[style, !scrollEnabled && styles.scrollDisabled, hideScrollbar && styles.hideScrollbar]} />;
});

// Chrome doesn't support e.preventDefault in this case; touch-action must be
// used to disable scrolling.
// https://developers.google.com/web/updates/2017/01/scrolling-intervention
const styles = StyleSheet.create({
  scrollDisabled: {
    overflowX: 'hidden',
    overflowY: 'hidden',
    touchAction: 'none'
  },
  hideScrollbar: {
    scrollbarWidth: 'none'
  }
});
export default ScrollViewBase;