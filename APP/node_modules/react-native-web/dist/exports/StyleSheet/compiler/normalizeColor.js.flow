/**
 * Copyright (c) <PERSON>.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 */

import isWebColor from '../../../modules/isWebColor';
import processColor from '../../../exports/processColor';
declare var normalizeColor: (color?: number | string, opacity?: number) => void | string;
export default normalizeColor;