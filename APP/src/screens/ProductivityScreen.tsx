import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  StatusBar,
  Dimensions,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useDynamicTheme } from '../contexts/DynamicThemeProvider';
import { ExpressiveTypographyVariants } from '../constants/expressiveTheme';
import { StudyTimer } from '../components/timer/StudyTimer';
import { TasksDropdown } from '../components/tasks/TasksDropdown';
import { ExamCountdown } from '../components/productivity/ExamCountdown';
import { SpotifyBar } from '../components/productivity/SpotifyBar';
import { ProductivitySettings } from '../components/productivity/ProductivitySettings';
import { useTimerStore } from '../stores/timerStore';
import { useAuthStore } from '../stores/authStore';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface ProductivityVisibilitySettings {
  showQuotes: boolean;
  showTasks: boolean;
  showExamCountdown: boolean;
  showSpotifyBar: boolean;
  spotifyCollapsed: boolean;
}

const DEFAULT_VISIBILITY_SETTINGS: ProductivityVisibilitySettings = {
  showQuotes: true,
  showTasks: true,
  showExamCountdown: true,
  showSpotifyBar: true,
  spotifyCollapsed: false,
};

export const ProductivityScreen: React.FC = () => {
  const theme = useDynamicTheme();
  const { user } = useAuthStore();
  const { mode, setMode } = useTimerStore();
  
  const [visibilitySettings, setVisibilitySettings] = useState<ProductivityVisibilitySettings>(
    DEFAULT_VISIBILITY_SETTINGS
  );
  const [isLoadingSettings, setIsLoadingSettings] = useState(true);
  const [quote, setQuote] = useState<string>('');
  const [showSettings, setShowSettings] = useState(false);

  // Animated values for background effects
  const fadeAnim = new Animated.Value(0);
  const scaleAnim = new Animated.Value(0.95);

  useEffect(() => {
    // Animate screen entrance
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Load productivity settings
    loadProductivitySettings();
  }, []);

  const loadProductivitySettings = async () => {
    if (!user) return;

    try {
      // For now, use default settings
      // TODO: Implement Supabase settings loading
      setVisibilitySettings(DEFAULT_VISIBILITY_SETTINGS);
    } catch (error) {
      console.error('Error fetching productivity settings:', error);
    } finally {
      setIsLoadingSettings(false);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      flexGrow: 1,
    },
    backgroundOrb: {
      position: 'absolute',
      borderRadius: 1000,
      opacity: 0.1,
    },
    orb1: {
      top: screenHeight * 0.1,
      right: screenWidth * 0.1,
      width: 200,
      height: 200,
      backgroundColor: theme.colors.primary,
    },
    orb2: {
      bottom: screenHeight * 0.15,
      left: screenWidth * 0.05,
      width: 250,
      height: 250,
      backgroundColor: theme.colors.secondary,
    },
    orb3: {
      top: screenHeight * 0.4,
      left: screenWidth * 0.2,
      width: 120,
      height: 120,
      backgroundColor: theme.colors.tertiary,
    },
    header: {
      paddingHorizontal: 20,
      paddingTop: 20,
      paddingBottom: 10,
    },
    title: {
      ...ExpressiveTypographyVariants.displayLarge,
      color: theme.colors.onBackground,
      textAlign: 'center',
      marginBottom: 8,
    },
    subtitle: {
      ...ExpressiveTypographyVariants.bodyLarge,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      opacity: 0.8,
    },
    mainContent: {
      flex: 1,
      paddingHorizontal: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    timerContainer: {
      width: '100%',
      maxWidth: 400,
      alignItems: 'center',
    },
    modeSelector: {
      flexDirection: 'row',
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 25,
      padding: 4,
      marginBottom: 40,
    },
    modeButton: {
      flex: 1,
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 21,
      alignItems: 'center',
    },
    activeModeButton: {
      backgroundColor: theme.colors.primary,
    },
    modeButtonText: {
      ...ExpressiveTypographyVariants.labelLarge,
      color: theme.colors.onSurfaceVariant,
    },
    activeModeButtonText: {
      color: theme.colors.onPrimary,
      fontWeight: '600',
    },
    componentsContainer: {
      paddingHorizontal: 20,
      paddingBottom: 100,
    },
    componentWrapper: {
      marginVertical: 10,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle={theme.dark ? 'light-content' : 'dark-content'}
        backgroundColor={theme.colors.background}
      />
      
      {/* Animated Background Orbs */}
      <Animated.View style={[styles.backgroundOrb, styles.orb1, { opacity: fadeAnim }]} />
      <Animated.View style={[styles.backgroundOrb, styles.orb2, { opacity: fadeAnim }]} />
      <Animated.View style={[styles.backgroundOrb, styles.orb3, { opacity: fadeAnim }]} />

      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={{ flexGrow: 1 }}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <Animated.View 
          style={[styles.header, { 
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }]
          }]}
        >
          <Text style={styles.title}>Productivity</Text>
          <Text style={styles.subtitle}>Focus • Study • Achieve</Text>
        </Animated.View>

        {/* Exam Countdown */}
        {visibilitySettings.showExamCountdown && (
          <Animated.View 
            style={[styles.componentWrapper, { 
              opacity: fadeAnim,
              transform: [{ translateY: Animated.multiply(scaleAnim, -20) }]
            }]}
          >
            <ExamCountdown />
          </Animated.View>
        )}

        {/* Tasks Dropdown */}
        {visibilitySettings.showTasks && (
          <Animated.View 
            style={[styles.componentWrapper, { 
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }]
            }]}
          >
            <TasksDropdown />
          </Animated.View>
        )}

        {/* Main Timer Content */}
        <Animated.View 
          style={[styles.mainContent, { 
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }]
          }]}
        >
          <View style={styles.timerContainer}>
            {/* Mode Selector */}
            <View style={styles.modeSelector}>
              <Animated.View
                style={[
                  styles.modeButton,
                  mode === 'pomodoro' && styles.activeModeButton,
                ]}
              >
                <Text
                  style={[
                    styles.modeButtonText,
                    mode === 'pomodoro' && styles.activeModeButtonText,
                  ]}
                  onPress={() => setMode('pomodoro')}
                >
                  Pomodoro
                </Text>
              </Animated.View>
              
              <Animated.View
                style={[
                  styles.modeButton,
                  mode === 'stopwatch' && styles.activeModeButton,
                ]}
              >
                <Text
                  style={[
                    styles.modeButtonText,
                    mode === 'stopwatch' && styles.activeModeButtonText,
                  ]}
                  onPress={() => setMode('stopwatch')}
                >
                  Stopwatch
                </Text>
              </Animated.View>
            </View>

            {/* Study Timer */}
            <StudyTimer mode={mode} />
          </View>
        </Animated.View>

        {/* Additional Components */}
        <View style={styles.componentsContainer}>
          {/* Spotify Bar */}
          {visibilitySettings.showSpotifyBar && (
            <Animated.View 
              style={[styles.componentWrapper, { 
                opacity: fadeAnim,
                transform: [{ translateY: Animated.multiply(scaleAnim, 20) }]
              }]}
            >
              <SpotifyBar 
                isCollapsed={visibilitySettings.spotifyCollapsed}
                onToggleCollapse={(collapsed) => 
                  setVisibilitySettings(prev => ({ ...prev, spotifyCollapsed: collapsed }))
                }
              />
            </Animated.View>
          )}
        </View>
      </ScrollView>

      {/* Settings Modal */}
      <ProductivitySettings
        visible={showSettings}
        onClose={() => setShowSettings(false)}
        settings={visibilitySettings}
        onSettingsChange={setVisibilitySettings}
      />
    </SafeAreaView>
  );
};
