import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Switch,
  ScrollView,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypographyVariants } from '../../constants/expressiveTheme';
import { MaterialIcons } from '@expo/vector-icons';

interface ProductivityVisibilitySettings {
  showQuotes: boolean;
  showTasks: boolean;
  showExamCountdown: boolean;
  showSpotifyBar: boolean;
  spotifyCollapsed: boolean;
}

interface ProductivitySettingsProps {
  visible: boolean;
  onClose: () => void;
  settings: ProductivityVisibilitySettings;
  onSettingsChange: (settings: ProductivityVisibilitySettings) => void;
}

export const ProductivitySettings: React.FC<ProductivitySettingsProps> = ({
  visible,
  onClose,
  settings,
  onSettingsChange,
}) => {
  const theme = useDynamicTheme();

  const updateSetting = (key: keyof ProductivityVisibilitySettings, value: boolean) => {
    onSettingsChange({
      ...settings,
      [key]: value,
    });
  };

  const styles = StyleSheet.create({
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      backgroundColor: theme.colors.surface,
      borderRadius: 24,
      padding: 24,
      margin: 20,
      maxHeight: '80%',
      width: '90%',
      elevation: 12,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: 0.4,
      shadowRadius: 12,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 24,
    },
    title: {
      ...ExpressiveTypographyVariants.headlineSmall,
      color: theme.colors.onSurface,
      fontWeight: '600',
    },
    closeButton: {
      padding: 8,
    },
    settingsContainer: {
      gap: 16,
    },
    settingItem: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: 12,
      paddingHorizontal: 16,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 12,
    },
    settingInfo: {
      flex: 1,
      marginRight: 16,
    },
    settingTitle: {
      ...ExpressiveTypographyVariants.titleSmall,
      color: theme.colors.onSurfaceVariant,
      fontWeight: '600',
      marginBottom: 2,
    },
    settingDescription: {
      ...ExpressiveTypographyVariants.bodySmall,
      color: theme.colors.onSurfaceVariant,
      opacity: 0.8,
    },
    sectionTitle: {
      ...ExpressiveTypographyVariants.titleMedium,
      color: theme.colors.onSurface,
      fontWeight: '600',
      marginTop: 16,
      marginBottom: 8,
    },
    resetButton: {
      backgroundColor: theme.colors.errorContainer,
      borderRadius: 12,
      paddingVertical: 14,
      paddingHorizontal: 20,
      alignItems: 'center',
      marginTop: 24,
    },
    resetButtonText: {
      ...ExpressiveTypographyVariants.labelLarge,
      color: theme.colors.onErrorContainer,
      fontWeight: '600',
    },
  });

  const resetToDefaults = () => {
    onSettingsChange({
      showQuotes: true,
      showTasks: true,
      showExamCountdown: true,
      showSpotifyBar: true,
      spotifyCollapsed: false,
    });
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.title}>Productivity Settings</Text>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <MaterialIcons
                name="close"
                size={24}
                color={theme.colors.onSurface}
              />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.settingsContainer} showsVerticalScrollIndicator={false}>
            <Text style={styles.sectionTitle}>Component Visibility</Text>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>Motivational Quotes</Text>
                <Text style={styles.settingDescription}>
                  Show inspirational quotes on the productivity screen
                </Text>
              </View>
              <Switch
                value={settings.showQuotes}
                onValueChange={(value) => updateSetting('showQuotes', value)}
                trackColor={{
                  false: theme.colors.outline,
                  true: theme.colors.primary,
                }}
                thumbColor={
                  settings.showQuotes
                    ? theme.colors.onPrimary
                    : theme.colors.onSurfaceVariant
                }
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>Tasks Panel</Text>
                <Text style={styles.settingDescription}>
                  Show the tasks dropdown and management interface
                </Text>
              </View>
              <Switch
                value={settings.showTasks}
                onValueChange={(value) => updateSetting('showTasks', value)}
                trackColor={{
                  false: theme.colors.outline,
                  true: theme.colors.primary,
                }}
                thumbColor={
                  settings.showTasks
                    ? theme.colors.onPrimary
                    : theme.colors.onSurfaceVariant
                }
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>Exam Countdown</Text>
                <Text style={styles.settingDescription}>
                  Display countdown timer for upcoming exams
                </Text>
              </View>
              <Switch
                value={settings.showExamCountdown}
                onValueChange={(value) => updateSetting('showExamCountdown', value)}
                trackColor={{
                  false: theme.colors.outline,
                  true: theme.colors.primary,
                }}
                thumbColor={
                  settings.showExamCountdown
                    ? theme.colors.onPrimary
                    : theme.colors.onSurfaceVariant
                }
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>Music Player</Text>
                <Text style={styles.settingDescription}>
                  Show Spotify integration for focus music
                </Text>
              </View>
              <Switch
                value={settings.showSpotifyBar}
                onValueChange={(value) => updateSetting('showSpotifyBar', value)}
                trackColor={{
                  false: theme.colors.outline,
                  true: theme.colors.primary,
                }}
                thumbColor={
                  settings.showSpotifyBar
                    ? theme.colors.onPrimary
                    : theme.colors.onSurfaceVariant
                }
              />
            </View>

            {settings.showSpotifyBar && (
              <>
                <Text style={styles.sectionTitle}>Music Player Settings</Text>
                
                <View style={styles.settingItem}>
                  <View style={styles.settingInfo}>
                    <Text style={styles.settingTitle}>Start Collapsed</Text>
                    <Text style={styles.settingDescription}>
                      Show music player in collapsed state by default
                    </Text>
                  </View>
                  <Switch
                    value={settings.spotifyCollapsed}
                    onValueChange={(value) => updateSetting('spotifyCollapsed', value)}
                    trackColor={{
                      false: theme.colors.outline,
                      true: theme.colors.primary,
                    }}
                    thumbColor={
                      settings.spotifyCollapsed
                        ? theme.colors.onPrimary
                        : theme.colors.onSurfaceVariant
                    }
                  />
                </View>
              </>
            )}

            <TouchableOpacity style={styles.resetButton} onPress={resetToDefaults}>
              <Text style={styles.resetButtonText}>Reset to Defaults</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};
