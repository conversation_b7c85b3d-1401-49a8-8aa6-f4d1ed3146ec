import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../../constants/expressiveTheme';
import { Achievement, useAchievementsStore } from '../../stores/achievementsStore';
import AchievementCard from './AchievementCard';
import ExpressiveCard from '../surfaces/ExpressiveCard';

interface AchievementsListProps {
  showCategories?: boolean;
  showProgress?: boolean;
  cardSize?: 'small' | 'medium' | 'large';
  onAchievementPress?: (achievement: Achievement) => void;
}

export const AchievementsList: React.FC<AchievementsListProps> = ({
  showCategories = true,
  showProgress = true,
  cardSize = 'medium',
  onAchievementPress,
}) => {
  const theme = useDynamicTheme();
  const {
    achievements,
    getUnlockedAchievements,
    getLockedAchievements,
    getAchievementsByCategory,
    getCompletionPercentage,
    userProgress,
  } = useAchievementsStore();

  const [selectedCategory, setSelectedCategory] = useState<Achievement['category'] | 'all'>('all');
  const [showUnlockedOnly, setShowUnlockedOnly] = useState(false);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    title: {
      ...ExpressiveTypography.headlineSmall,
      color: theme.colors.onSurface,
      marginBottom: 8,
    },
    progressContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    progressBar: {
      flex: 1,
      height: 8,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 4,
      marginRight: 12,
      overflow: 'hidden',
    },
    progressFill: {
      height: '100%',
      backgroundColor: theme.colors.primary,
      borderRadius: 4,
    },
    progressText: {
      ...ExpressiveTypography.labelMedium,
      color: theme.colors.onSurface,
      fontWeight: '600',
    },
    statsContainer: {
      flexDirection: 'row',
      gap: 12,
    },
    statCard: {
      flex: 1,
      backgroundColor: theme.colors.primaryContainer,
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 8,
      alignItems: 'center',
    },
    statValue: {
      ...ExpressiveTypography.titleSmall,
      color: theme.colors.onPrimaryContainer,
      fontWeight: '600',
    },
    statLabel: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onPrimaryContainer,
      marginTop: 2,
    },
    filtersContainer: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: theme.colors.surfaceContainer,
    },
    filterRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    filterLabel: {
      ...ExpressiveTypography.labelMedium,
      color: theme.colors.onSurface,
      marginRight: 12,
      minWidth: 60,
    },
    categoryFilters: {
      flexDirection: 'row',
      gap: 8,
    },
    categoryButton: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.outline,
    },
    selectedCategoryButton: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    categoryButtonText: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurface,
    },
    selectedCategoryButtonText: {
      color: theme.colors.onPrimary,
      fontWeight: '600',
    },
    toggleButton: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.outline,
    },
    selectedToggleButton: {
      backgroundColor: theme.colors.secondary,
      borderColor: theme.colors.secondary,
    },
    toggleButtonText: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurface,
    },
    selectedToggleButtonText: {
      color: theme.colors.onSecondary,
      fontWeight: '600',
    },
    content: {
      flex: 1,
    },
    sectionHeader: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: theme.colors.surfaceVariant,
    },
    sectionTitle: {
      ...ExpressiveTypography.titleSmall,
      color: theme.colors.onSurfaceVariant,
      fontWeight: '600',
      textTransform: 'uppercase',
    },
    achievementsList: {
      padding: 16,
      gap: 12,
    },
    emptyState: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 32,
    },
    emptyText: {
      ...ExpressiveTypography.bodyLarge,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginTop: 16,
    },
  });

  // Filter achievements based on selected category and unlock status
  const filteredAchievements = useMemo(() => {
    let filtered = achievements;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = getAchievementsByCategory(selectedCategory);
    }

    // Filter by unlock status
    if (showUnlockedOnly) {
      filtered = filtered.filter(achievement => achievement.isUnlocked);
    }

    // Sort: unlocked first, then by tier, then by progress
    return filtered.sort((a, b) => {
      if (a.isUnlocked !== b.isUnlocked) {
        return a.isUnlocked ? -1 : 1;
      }
      
      const tierOrder = { bronze: 1, silver: 2, gold: 3, platinum: 4, diamond: 5 };
      const tierDiff = tierOrder[a.tier] - tierOrder[b.tier];
      if (tierDiff !== 0) return tierDiff;
      
      return b.progress - a.progress;
    });
  }, [achievements, selectedCategory, showUnlockedOnly, getAchievementsByCategory]);

  // Group achievements by category for display
  const groupedAchievements = useMemo(() => {
    if (!showCategories || selectedCategory !== 'all') {
      return [{ category: selectedCategory, achievements: filteredAchievements }];
    }

    const categories: Achievement['category'][] = ['milestone', 'streak', 'time', 'sessions', 'productivity', 'special'];
    
    return categories.map(category => ({
      category,
      achievements: filteredAchievements.filter(achievement => achievement.category === category),
    })).filter(group => group.achievements.length > 0);
  }, [filteredAchievements, showCategories, selectedCategory]);

  const categories: { key: Achievement['category'] | 'all'; label: string; icon: string }[] = [
    { key: 'all', label: 'All', icon: '🏆' },
    { key: 'milestone', label: 'Milestones', icon: '🎯' },
    { key: 'streak', label: 'Streaks', icon: '🔥' },
    { key: 'time', label: 'Time', icon: '⏰' },
    { key: 'sessions', label: 'Sessions', icon: '📚' },
    { key: 'productivity', label: 'Quality', icon: '⭐' },
    { key: 'special', label: 'Special', icon: '🎪' },
  ];

  const completionPercentage = getCompletionPercentage();
  const unlockedCount = getUnlockedAchievements().length;
  const totalCount = achievements.filter(a => !a.isSecret).length;

  const renderCategoryLabel = (category: Achievement['category'] | 'all'): string => {
    const categoryData = categories.find(c => c.key === category);
    return categoryData ? `${categoryData.icon} ${categoryData.label}` : 'Unknown';
  };

  const renderAchievementItem = ({ item, index }: { item: Achievement; index: number }) => (
    <AchievementCard
      achievement={item}
      onPress={onAchievementPress}
      showProgress={showProgress}
      size={cardSize}
      animationDelay={index * 100}
    />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={{ fontSize: 48 }}>🏆</Text>
      <Text style={styles.emptyText}>
        {showUnlockedOnly 
          ? "No achievements unlocked in this category yet.\nKeep studying to earn your first achievement!"
          : "No achievements found for the selected filters."
        }
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header with Progress */}
      <View style={styles.header}>
        <Text style={styles.title}>Achievements</Text>
        
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: `${completionPercentage}%` }]} />
          </View>
          <Text style={styles.progressText}>
            {completionPercentage.toFixed(0)}%
          </Text>
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statValue}>{unlockedCount}</Text>
            <Text style={styles.statLabel}>Unlocked</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statValue}>{totalCount}</Text>
            <Text style={styles.statLabel}>Total</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statValue}>{userProgress.level}</Text>
            <Text style={styles.statLabel}>Level</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statValue}>{userProgress.totalPoints}</Text>
            <Text style={styles.statLabel}>Points</Text>
          </View>
        </View>
      </View>

      {/* Filters */}
      <View style={styles.filtersContainer}>
        <View style={styles.filterRow}>
          <Text style={styles.filterLabel}>Category:</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.categoryFilters}>
              {categories.map(category => (
                <TouchableOpacity
                  key={category.key}
                  style={[
                    styles.categoryButton,
                    selectedCategory === category.key && styles.selectedCategoryButton,
                  ]}
                  onPress={() => setSelectedCategory(category.key)}
                >
                  <Text
                    style={[
                      styles.categoryButtonText,
                      selectedCategory === category.key && styles.selectedCategoryButtonText,
                    ]}
                  >
                    {category.icon} {category.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        <View style={styles.filterRow}>
          <Text style={styles.filterLabel}>Show:</Text>
          <TouchableOpacity
            style={[
              styles.toggleButton,
              showUnlockedOnly && styles.selectedToggleButton,
            ]}
            onPress={() => setShowUnlockedOnly(!showUnlockedOnly)}
          >
            <Text
              style={[
                styles.toggleButtonText,
                showUnlockedOnly && styles.selectedToggleButtonText,
              ]}
            >
              {showUnlockedOnly ? '✓ Unlocked Only' : 'All Achievements'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {filteredAchievements.length === 0 ? (
          renderEmptyState()
        ) : (
          <FlatList
            data={filteredAchievements}
            renderItem={renderAchievementItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.achievementsList}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </View>
  );
};

export default AchievementsList;
