import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TextInput,
  TouchableOpacity,
  Alert,
  Animated,
  Share,
  Clipboard,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypographyVariants } from '../../constants/expressiveTheme';
import { MaterialIcons } from '@expo/vector-icons';
import { useGroupsStore } from '../../stores/groupsStore';
import { useAuthStore } from '../../stores/authStore';
import { joinGroupByInviteCode, regenerateInviteCode } from '../../services/supabase/groups';

interface InviteCodeModalProps {
  visible: boolean;
  onClose: () => void;
  mode: 'share' | 'join';
  group?: any; // For share mode
  onGroupJoined?: (group: any) => void;
}

export const InviteCodeModal: React.FC<InviteCodeModalProps> = ({
  visible,
  onClose,
  mode,
  group,
  onGroupJoined,
}) => {
  const theme = useDynamicTheme();
  const { user } = useAuthStore();
  const { loading } = useGroupsStore();

  const [inviteCode, setInviteCode] = useState('');
  const [inputCode, setInputCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Animation values
  const fadeAnim = new Animated.Value(0);
  const scaleAnim = new Animated.Value(0.95);
  const pulseAnim = new Animated.Value(1);

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();

      if (mode === 'share' && group?.invite_code) {
        setInviteCode(group.invite_code);
      }
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible, mode, group]);

  const handleCopyCode = async () => {
    try {
      await Clipboard.setString(inviteCode);
      
      // Pulse animation for feedback
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();

      Alert.alert('Copied!', 'Invite code copied to clipboard');
    } catch (error) {
      Alert.alert('Error', 'Failed to copy invite code');
    }
  };

  const handleShareCode = async () => {
    try {
      const shareMessage = `Join my study group "${group?.name}" on IsotopeAI!\n\nUse invite code: ${inviteCode}\n\nLet's study together and achieve our goals! 📚✨`;
      
      await Share.share({
        message: shareMessage,
        title: `Join ${group?.name} on IsotopeAI`,
      });
    } catch (error) {
      console.error('Error sharing invite code:', error);
    }
  };

  const handleRegenerateCode = async () => {
    if (!group) return;

    Alert.alert(
      'Regenerate Invite Code',
      'This will invalidate the current invite code. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Regenerate',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);
              const newCode = await regenerateInviteCode(group.id);
              setInviteCode(newCode);
              Alert.alert('Success', 'New invite code generated!');
            } catch (error) {
              Alert.alert('Error', 'Failed to regenerate invite code');
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  const handleJoinGroup = async () => {
    if (!inputCode.trim()) {
      Alert.alert('Error', 'Please enter an invite code');
      return;
    }

    if (!user) {
      Alert.alert('Error', 'You must be logged in to join a group');
      return;
    }

    try {
      setIsLoading(true);
      const joinedGroup = await joinGroupByInviteCode(inputCode.trim().toUpperCase(), user.id);
      
      Alert.alert(
        'Success!',
        `You've joined "${joinedGroup.name}" successfully!`,
        [
          {
            text: 'OK',
            onPress: () => {
              onGroupJoined?.(joinedGroup);
              handleClose();
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error joining group:', error);
      Alert.alert(
        'Error',
        error instanceof Error ? error.message : 'Failed to join group. Please check the invite code and try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setInputCode('');
    onClose();
  };

  const styles = StyleSheet.create({
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      backgroundColor: theme.colors.surface,
      borderRadius: 24,
      padding: 24,
      margin: 20,
      width: '90%',
      elevation: 12,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: 0.4,
      shadowRadius: 12,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 24,
    },
    title: {
      ...ExpressiveTypographyVariants.headlineSmall,
      color: theme.colors.onSurface,
      fontWeight: '600',
    },
    closeButton: {
      padding: 8,
    },
    content: {
      gap: 20,
    },
    groupInfo: {
      backgroundColor: theme.colors.primaryContainer,
      borderRadius: 16,
      padding: 16,
      alignItems: 'center',
    },
    groupName: {
      ...ExpressiveTypographyVariants.titleMedium,
      color: theme.colors.onPrimaryContainer,
      fontWeight: '600',
      textAlign: 'center',
      marginBottom: 4,
    },
    groupDescription: {
      ...ExpressiveTypographyVariants.bodyMedium,
      color: theme.colors.onPrimaryContainer,
      textAlign: 'center',
      opacity: 0.8,
    },
    codeContainer: {
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 16,
      padding: 20,
      alignItems: 'center',
      gap: 12,
    },
    codeLabel: {
      ...ExpressiveTypographyVariants.labelLarge,
      color: theme.colors.onSurfaceVariant,
      fontWeight: '600',
    },
    codeDisplay: {
      backgroundColor: theme.colors.surface,
      borderRadius: 12,
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderWidth: 2,
      borderColor: theme.colors.primary,
      borderStyle: 'dashed',
    },
    codeText: {
      ...ExpressiveTypographyVariants.headlineMedium,
      color: theme.colors.primary,
      fontWeight: '700',
      letterSpacing: 2,
      textAlign: 'center',
    },
    codeActions: {
      flexDirection: 'row',
      gap: 12,
    },
    actionButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 12,
      gap: 8,
    },
    copyButton: {
      backgroundColor: theme.colors.secondaryContainer,
    },
    shareButton: {
      backgroundColor: theme.colors.tertiaryContainer,
    },
    regenerateButton: {
      backgroundColor: theme.colors.errorContainer,
      marginTop: 8,
    },
    actionButtonText: {
      ...ExpressiveTypographyVariants.labelMedium,
      fontWeight: '600',
    },
    copyButtonText: {
      color: theme.colors.onSecondaryContainer,
    },
    shareButtonText: {
      color: theme.colors.onTertiaryContainer,
    },
    regenerateButtonText: {
      color: theme.colors.onErrorContainer,
    },
    inputContainer: {
      gap: 12,
    },
    inputLabel: {
      ...ExpressiveTypographyVariants.labelLarge,
      color: theme.colors.onSurface,
      fontWeight: '600',
      textAlign: 'center',
    },
    input: {
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 16,
      color: theme.colors.onSurfaceVariant,
      ...ExpressiveTypographyVariants.headlineSmall,
      textAlign: 'center',
      letterSpacing: 2,
      textTransform: 'uppercase',
      borderWidth: 1,
      borderColor: 'transparent',
    },
    inputFocused: {
      borderColor: theme.colors.primary,
    },
    joinButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      paddingVertical: 16,
      alignItems: 'center',
      justifyContent: 'center',
    },
    joinButtonDisabled: {
      backgroundColor: theme.colors.surfaceDisabled,
    },
    joinButtonText: {
      ...ExpressiveTypographyVariants.labelLarge,
      color: theme.colors.onPrimary,
      fontWeight: '600',
    },
    joinButtonTextDisabled: {
      color: theme.colors.onSurfaceDisabled,
    },
    loadingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    instructions: {
      ...ExpressiveTypographyVariants.bodyMedium,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      opacity: 0.8,
    },
  });

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={handleClose}
    >
      <View style={styles.modalOverlay}>
        <Animated.View
          style={[
            styles.modalContent,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <View style={styles.header}>
            <Text style={styles.title}>
              {mode === 'share' ? 'Share Group' : 'Join Group'}
            </Text>
            <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
              <MaterialIcons
                name="close"
                size={24}
                color={theme.colors.onSurface}
              />
            </TouchableOpacity>
          </View>

          <View style={styles.content}>
            {mode === 'share' ? (
              <>
                {/* Group Info */}
                {group && (
                  <View style={styles.groupInfo}>
                    <Text style={styles.groupName}>{group.name}</Text>
                    {group.description && (
                      <Text style={styles.groupDescription}>
                        {group.description}
                      </Text>
                    )}
                  </View>
                )}

                {/* Invite Code Display */}
                <View style={styles.codeContainer}>
                  <Text style={styles.codeLabel}>Invite Code</Text>
                  <Animated.View
                    style={[
                      styles.codeDisplay,
                      { transform: [{ scale: pulseAnim }] },
                    ]}
                  >
                    <Text style={styles.codeText}>{inviteCode}</Text>
                  </Animated.View>

                  <View style={styles.codeActions}>
                    <TouchableOpacity
                      style={[styles.actionButton, styles.copyButton]}
                      onPress={handleCopyCode}
                    >
                      <MaterialIcons
                        name="content-copy"
                        size={16}
                        color={theme.colors.onSecondaryContainer}
                      />
                      <Text style={[styles.actionButtonText, styles.copyButtonText]}>
                        Copy
                      </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                      style={[styles.actionButton, styles.shareButton]}
                      onPress={handleShareCode}
                    >
                      <MaterialIcons
                        name="share"
                        size={16}
                        color={theme.colors.onTertiaryContainer}
                      />
                      <Text style={[styles.actionButtonText, styles.shareButtonText]}>
                        Share
                      </Text>
                    </TouchableOpacity>
                  </View>

                  <TouchableOpacity
                    style={[styles.actionButton, styles.regenerateButton]}
                    onPress={handleRegenerateCode}
                    disabled={isLoading}
                  >
                    <MaterialIcons
                      name="refresh"
                      size={16}
                      color={theme.colors.onErrorContainer}
                    />
                    <Text style={[styles.actionButtonText, styles.regenerateButtonText]}>
                      {isLoading ? 'Regenerating...' : 'Regenerate Code'}
                    </Text>
                  </TouchableOpacity>
                </View>
              </>
            ) : (
              <>
                {/* Join Group Interface */}
                <Text style={styles.instructions}>
                  Enter the invite code shared by a group member to join their study group.
                </Text>

                <View style={styles.inputContainer}>
                  <Text style={styles.inputLabel}>Enter Invite Code</Text>
                  <TextInput
                    style={[
                      styles.input,
                      inputCode && styles.inputFocused,
                    ]}
                    placeholder="ABCD1234"
                    placeholderTextColor={theme.colors.onSurfaceVariant}
                    value={inputCode}
                    onChangeText={(text) => setInputCode(text.toUpperCase())}
                    maxLength={8}
                    autoCapitalize="characters"
                    autoCorrect={false}
                  />

                  <TouchableOpacity
                    style={[
                      styles.joinButton,
                      (!inputCode.trim() || isLoading) && styles.joinButtonDisabled,
                    ]}
                    onPress={handleJoinGroup}
                    disabled={!inputCode.trim() || isLoading}
                  >
                    {isLoading ? (
                      <View style={styles.loadingContainer}>
                        <MaterialIcons
                          name="hourglass-empty"
                          size={16}
                          color={theme.colors.onSurfaceDisabled}
                        />
                        <Text style={[styles.joinButtonText, styles.joinButtonTextDisabled]}>
                          Joining...
                        </Text>
                      </View>
                    ) : (
                      <Text style={styles.joinButtonText}>Join Group</Text>
                    )}
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};
