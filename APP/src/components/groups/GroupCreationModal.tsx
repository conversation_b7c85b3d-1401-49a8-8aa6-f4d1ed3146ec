import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TextInput,
  TouchableOpacity,
  Switch,
  ScrollView,
  Alert,
  Animated,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypographyVariants } from '../../constants/expressiveTheme';
import { MaterialIcons } from '@expo/vector-icons';
import { useGroupsStore } from '../../stores/groupsStore';
import { useAuthStore } from '../../stores/authStore';

interface GroupCreationModalProps {
  visible: boolean;
  onClose: () => void;
  onGroupCreated?: (group: any) => void;
}

export const GroupCreationModal: React.FC<GroupCreationModalProps> = ({
  visible,
  onClose,
  onGroupCreated,
}) => {
  const theme = useDynamicTheme();
  const { user } = useAuthStore();
  const { createGroup, loading } = useGroupsStore();

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isPublic: false,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Animation values
  const fadeAnim = new Animated.Value(0);
  const scaleAnim = new Animated.Value(0.95);

  React.useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Group name is required';
    } else if (formData.name.trim().length < 3) {
      newErrors.name = 'Group name must be at least 3 characters';
    } else if (formData.name.trim().length > 50) {
      newErrors.name = 'Group name must be less than 50 characters';
    }

    if (formData.description.length > 200) {
      newErrors.description = 'Description must be less than 200 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreateGroup = async () => {
    if (!validateForm()) return;
    if (!user) {
      Alert.alert('Error', 'You must be logged in to create a group');
      return;
    }

    try {
      const group = await createGroup({
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        is_public: formData.isPublic,
        created_by: user.id,
      });

      Alert.alert(
        'Success!',
        `Group "${group.name}" has been created successfully.`,
        [
          {
            text: 'OK',
            onPress: () => {
              onGroupCreated?.(group);
              handleClose();
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error creating group:', error);
      Alert.alert(
        'Error',
        'Failed to create group. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleClose = () => {
    setFormData({
      name: '',
      description: '',
      isPublic: false,
    });
    setErrors({});
    onClose();
  };

  const styles = StyleSheet.create({
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContent: {
      backgroundColor: theme.colors.surface,
      borderRadius: 24,
      padding: 24,
      margin: 20,
      maxHeight: '80%',
      width: '90%',
      elevation: 12,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: 0.4,
      shadowRadius: 12,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 24,
    },
    title: {
      ...ExpressiveTypographyVariants.headlineSmall,
      color: theme.colors.onSurface,
      fontWeight: '600',
    },
    closeButton: {
      padding: 8,
    },
    form: {
      gap: 20,
    },
    inputGroup: {
      gap: 8,
    },
    label: {
      ...ExpressiveTypographyVariants.labelLarge,
      color: theme.colors.onSurface,
      fontWeight: '600',
    },
    input: {
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      color: theme.colors.onSurfaceVariant,
      ...ExpressiveTypographyVariants.bodyLarge,
      borderWidth: 1,
      borderColor: 'transparent',
    },
    inputError: {
      borderColor: theme.colors.error,
    },
    textArea: {
      height: 100,
      textAlignVertical: 'top',
    },
    errorText: {
      ...ExpressiveTypographyVariants.bodySmall,
      color: theme.colors.error,
      marginTop: 4,
    },
    switchContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 12,
      padding: 16,
    },
    switchInfo: {
      flex: 1,
      marginRight: 16,
    },
    switchTitle: {
      ...ExpressiveTypographyVariants.titleSmall,
      color: theme.colors.onSurfaceVariant,
      fontWeight: '600',
      marginBottom: 4,
    },
    switchDescription: {
      ...ExpressiveTypographyVariants.bodySmall,
      color: theme.colors.onSurfaceVariant,
      opacity: 0.8,
    },
    actionButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 12,
      marginTop: 24,
    },
    button: {
      flex: 1,
      paddingVertical: 14,
      borderRadius: 12,
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: 48,
    },
    primaryButton: {
      backgroundColor: theme.colors.primary,
    },
    primaryButtonDisabled: {
      backgroundColor: theme.colors.surfaceDisabled,
    },
    secondaryButton: {
      backgroundColor: theme.colors.surfaceVariant,
    },
    buttonText: {
      ...ExpressiveTypographyVariants.labelLarge,
      fontWeight: '600',
    },
    primaryButtonText: {
      color: theme.colors.onPrimary,
    },
    primaryButtonTextDisabled: {
      color: theme.colors.onSurfaceDisabled,
    },
    secondaryButtonText: {
      color: theme.colors.onSurfaceVariant,
    },
    loadingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    characterCount: {
      ...ExpressiveTypographyVariants.bodySmall,
      color: theme.colors.onSurfaceVariant,
      opacity: 0.6,
      textAlign: 'right',
      marginTop: 4,
    },
    characterCountError: {
      color: theme.colors.error,
    },
  });

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={handleClose}
    >
      <View style={styles.modalOverlay}>
        <Animated.View
          style={[
            styles.modalContent,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          <View style={styles.header}>
            <Text style={styles.title}>Create New Group</Text>
            <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
              <MaterialIcons
                name="close"
                size={24}
                color={theme.colors.onSurface}
              />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.form} showsVerticalScrollIndicator={false}>
            {/* Group Name */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Group Name *</Text>
              <TextInput
                style={[
                  styles.input,
                  errors.name && styles.inputError,
                ]}
                placeholder="Enter group name"
                placeholderTextColor={theme.colors.onSurfaceVariant}
                value={formData.name}
                onChangeText={(text) => {
                  setFormData(prev => ({ ...prev, name: text }));
                  if (errors.name) {
                    setErrors(prev => ({ ...prev, name: '' }));
                  }
                }}
                maxLength={50}
              />
              {errors.name && (
                <Text style={styles.errorText}>{errors.name}</Text>
              )}
              <Text
                style={[
                  styles.characterCount,
                  formData.name.length > 45 && styles.characterCountError,
                ]}
              >
                {formData.name.length}/50
              </Text>
            </View>

            {/* Description */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Description</Text>
              <TextInput
                style={[
                  styles.input,
                  styles.textArea,
                  errors.description && styles.inputError,
                ]}
                placeholder="Describe your group's purpose (optional)"
                placeholderTextColor={theme.colors.onSurfaceVariant}
                value={formData.description}
                onChangeText={(text) => {
                  setFormData(prev => ({ ...prev, description: text }));
                  if (errors.description) {
                    setErrors(prev => ({ ...prev, description: '' }));
                  }
                }}
                multiline
                maxLength={200}
              />
              {errors.description && (
                <Text style={styles.errorText}>{errors.description}</Text>
              )}
              <Text
                style={[
                  styles.characterCount,
                  formData.description.length > 180 && styles.characterCountError,
                ]}
              >
                {formData.description.length}/200
              </Text>
            </View>

            {/* Privacy Setting */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Privacy Settings</Text>
              <View style={styles.switchContainer}>
                <View style={styles.switchInfo}>
                  <Text style={styles.switchTitle}>Public Group</Text>
                  <Text style={styles.switchDescription}>
                    {formData.isPublic
                      ? 'Anyone can discover and join this group'
                      : 'Only people with an invite can join this group'}
                  </Text>
                </View>
                <Switch
                  value={formData.isPublic}
                  onValueChange={(value) =>
                    setFormData(prev => ({ ...prev, isPublic: value }))
                  }
                  trackColor={{
                    false: theme.colors.outline,
                    true: theme.colors.primary,
                  }}
                  thumbColor={
                    formData.isPublic
                      ? theme.colors.onPrimary
                      : theme.colors.onSurfaceVariant
                  }
                />
              </View>
            </View>
          </ScrollView>

          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.button, styles.secondaryButton]}
              onPress={handleClose}
              disabled={loading}
            >
              <Text style={[styles.buttonText, styles.secondaryButtonText]}>
                Cancel
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.button,
                loading || !formData.name.trim()
                  ? styles.primaryButtonDisabled
                  : styles.primaryButton,
              ]}
              onPress={handleCreateGroup}
              disabled={loading || !formData.name.trim()}
            >
              {loading ? (
                <View style={styles.loadingContainer}>
                  <MaterialIcons
                    name="hourglass-empty"
                    size={16}
                    color={theme.colors.onSurfaceDisabled}
                  />
                  <Text
                    style={[styles.buttonText, styles.primaryButtonTextDisabled]}
                  >
                    Creating...
                  </Text>
                </View>
              ) : (
                <Text style={[styles.buttonText, styles.primaryButtonText]}>
                  Create Group
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};
