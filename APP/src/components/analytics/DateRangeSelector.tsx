import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  Dimensions,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../../constants/expressiveTheme';
import { DateRange } from '../../stores/analyticsStore';
import ExpressiveCard from '../surfaces/ExpressiveCard';
import ExpressiveButton from '../buttons/ExpressiveButton';

const { width: screenWidth } = Dimensions.get('window');

interface DateRangeSelectorProps {
  selectedRange: DateRange;
  onRangeChange: (range: DateRange) => void;
  showCustomPicker?: boolean;
}

interface PresetOption {
  id: DateRange['preset'];
  label: string;
  description: string;
  getDates: () => { start: Date; end: Date };
}

export const DateRangeSelector: React.FC<DateRangeSelectorProps> = ({
  selectedRange,
  onRangeChange,
  showCustomPicker = true,
}) => {
  const theme = useDynamicTheme();
  const [isModalVisible, setIsModalVisible] = useState(false);

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
    },
    selectorButton: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: theme.colors.surfaceContainer,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme.colors.outline,
    },
    selectorText: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onSurface,
      flex: 1,
    },
    selectorIcon: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onSurfaceVariant,
      marginLeft: 8,
    },
    modal: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end',
    },
    modalContent: {
      backgroundColor: theme.colors.surface,
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
      maxHeight: '80%',
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    modalTitle: {
      ...ExpressiveTypography.headlineSmall,
      color: theme.colors.onSurface,
    },
    closeButton: {
      padding: 8,
    },
    closeButtonText: {
      ...ExpressiveTypography.labelLarge,
      color: theme.colors.primary,
    },
    presetsList: {
      padding: 16,
    },
    presetItem: {
      paddingVertical: 16,
      paddingHorizontal: 16,
      borderRadius: 12,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: theme.colors.outline,
      backgroundColor: theme.colors.surface,
    },
    selectedPresetItem: {
      backgroundColor: theme.colors.primaryContainer,
      borderColor: theme.colors.primary,
    },
    presetLabel: {
      ...ExpressiveTypography.titleSmall,
      color: theme.colors.onSurface,
      marginBottom: 4,
    },
    selectedPresetLabel: {
      color: theme.colors.onPrimaryContainer,
    },
    presetDescription: {
      ...ExpressiveTypography.bodySmall,
      color: theme.colors.onSurfaceVariant,
    },
    selectedPresetDescription: {
      color: theme.colors.onPrimaryContainer,
    },
    presetDates: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.primary,
      marginTop: 4,
    },
    quickSelectContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      marginBottom: 16,
    },
    quickSelectButton: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      backgroundColor: theme.colors.surfaceVariant,
      borderWidth: 1,
      borderColor: theme.colors.outline,
    },
    selectedQuickSelectButton: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    quickSelectText: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurfaceVariant,
    },
    selectedQuickSelectText: {
      color: theme.colors.onPrimary,
    },
    customDateContainer: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outline,
    },
    customDateTitle: {
      ...ExpressiveTypography.titleSmall,
      color: theme.colors.onSurface,
      marginBottom: 12,
    },
    dateInputRow: {
      flexDirection: 'row',
      gap: 12,
      marginBottom: 16,
    },
    dateInput: {
      flex: 1,
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: theme.colors.outline,
      backgroundColor: theme.colors.surface,
    },
    dateInputText: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onSurface,
    },
    applyButton: {
      marginTop: 8,
    },
  });

  // Preset options
  const presetOptions: PresetOption[] = [
    {
      id: 'today',
      label: 'Today',
      description: 'Current day activity',
      getDates: () => {
        const today = new Date();
        const start = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const end = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
        return { start, end };
      },
    },
    {
      id: 'yesterday',
      label: 'Yesterday',
      description: 'Previous day activity',
      getDates: () => {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const start = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
        const end = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59);
        return { start, end };
      },
    },
    {
      id: 'week',
      label: 'This Week',
      description: 'Last 7 days including today',
      getDates: () => {
        const end = new Date();
        const start = new Date();
        start.setDate(start.getDate() - 6);
        start.setHours(0, 0, 0, 0);
        end.setHours(23, 59, 59, 999);
        return { start, end };
      },
    },
    {
      id: 'month',
      label: 'This Month',
      description: 'Last 30 days including today',
      getDates: () => {
        const end = new Date();
        const start = new Date();
        start.setDate(start.getDate() - 29);
        start.setHours(0, 0, 0, 0);
        end.setHours(23, 59, 59, 999);
        return { start, end };
      },
    },
    {
      id: 'year',
      label: 'This Year',
      description: 'Last 365 days including today',
      getDates: () => {
        const end = new Date();
        const start = new Date();
        start.setDate(start.getDate() - 364);
        start.setHours(0, 0, 0, 0);
        end.setHours(23, 59, 59, 999);
        return { start, end };
      },
    },
  ];

  // Quick select options (commonly used ranges)
  const quickSelectOptions = [
    { id: 'today', label: 'Today' },
    { id: 'week', label: '7 Days' },
    { id: 'month', label: '30 Days' },
    { id: 'year', label: '1 Year' },
  ];

  // Format date range for display
  const formatDateRange = useCallback((range: DateRange): string => {
    const formatDate = (date: Date) => {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined,
      });
    };

    if (range.preset !== 'custom') {
      const preset = presetOptions.find(p => p.id === range.preset);
      return preset?.label || 'Custom Range';
    }

    const startStr = formatDate(range.start);
    const endStr = formatDate(range.end);
    
    if (startStr === endStr) {
      return startStr;
    }
    
    return `${startStr} - ${endStr}`;
  }, []);

  // Handle preset selection
  const handlePresetSelect = useCallback((preset: DateRange['preset']) => {
    const option = presetOptions.find(p => p.id === preset);
    if (option) {
      const { start, end } = option.getDates();
      onRangeChange({ start, end, preset });
      setIsModalVisible(false);
    }
  }, [onRangeChange]);

  // Handle quick select
  const handleQuickSelect = useCallback((presetId: string) => {
    const preset = presetId as DateRange['preset'];
    handlePresetSelect(preset);
  }, [handlePresetSelect]);

  // Format date for preset display
  const formatPresetDates = useCallback((option: PresetOption): string => {
    const { start, end } = option.getDates();
    const formatDate = (date: Date) => date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    });
    
    if (option.id === 'today' || option.id === 'yesterday') {
      return formatDate(start);
    }
    
    return `${formatDate(start)} - ${formatDate(end)}`;
  }, []);

  return (
    <View style={styles.container}>
      {/* Selector Button */}
      <TouchableOpacity
        style={styles.selectorButton}
        onPress={() => setIsModalVisible(true)}
        activeOpacity={0.7}
      >
        <Text style={styles.selectorText}>
          {formatDateRange(selectedRange)}
        </Text>
        <Text style={styles.selectorIcon}>📅</Text>
      </TouchableOpacity>

      {/* Selection Modal */}
      <Modal
        visible={isModalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <View style={styles.modal}>
          <View style={styles.modalContent}>
            {/* Header */}
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Time Period</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setIsModalVisible(false)}
              >
                <Text style={styles.closeButtonText}>Done</Text>
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.presetsList} showsVerticalScrollIndicator={false}>
              {/* Quick Select */}
              <View style={styles.quickSelectContainer}>
                {quickSelectOptions.map((option) => (
                  <TouchableOpacity
                    key={option.id}
                    style={[
                      styles.quickSelectButton,
                      selectedRange.preset === option.id && styles.selectedQuickSelectButton,
                    ]}
                    onPress={() => handleQuickSelect(option.id)}
                  >
                    <Text
                      style={[
                        styles.quickSelectText,
                        selectedRange.preset === option.id && styles.selectedQuickSelectText,
                      ]}
                    >
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Preset Options */}
              {presetOptions.map((option) => {
                const isSelected = selectedRange.preset === option.id;
                
                return (
                  <TouchableOpacity
                    key={option.id}
                    style={[
                      styles.presetItem,
                      isSelected && styles.selectedPresetItem,
                    ]}
                    onPress={() => handlePresetSelect(option.id)}
                    activeOpacity={0.7}
                  >
                    <Text
                      style={[
                        styles.presetLabel,
                        isSelected && styles.selectedPresetLabel,
                      ]}
                    >
                      {option.label}
                    </Text>
                    <Text
                      style={[
                        styles.presetDescription,
                        isSelected && styles.selectedPresetDescription,
                      ]}
                    >
                      {option.description}
                    </Text>
                    <Text style={styles.presetDates}>
                      {formatPresetDates(option)}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>

            {/* Custom Date Range (placeholder for future implementation) */}
            {showCustomPicker && (
              <View style={styles.customDateContainer}>
                <Text style={styles.customDateTitle}>Custom Range</Text>
                <Text style={styles.presetDescription}>
                  Custom date picker will be available in a future update
                </Text>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default DateRangeSelector;
