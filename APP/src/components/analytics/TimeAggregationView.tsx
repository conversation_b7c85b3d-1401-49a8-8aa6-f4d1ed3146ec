import React, { useMemo, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import {
  VictoryChart,
  VictoryLine,
  VictoryBar,
  VictoryArea,
  VictoryAxis,
  VictoryTheme,
  VictoryTooltip,
  VictoryContainer,
} from 'victory-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../../constants/expressiveTheme';
import { StudySession, DateRange } from '../../stores/analyticsStore';
import ExpressiveCard from '../surfaces/ExpressiveCard';

const { width: screenWidth } = Dimensions.get('window');

interface TimeAggregationViewProps {
  sessions: StudySession[];
  dateRange: DateRange;
  aggregationType: 'daily' | 'weekly' | 'monthly';
  chartType?: 'line' | 'bar' | 'area';
  onAggregationChange?: (type: 'daily' | 'weekly' | 'monthly') => void;
  onChartTypeChange?: (type: 'line' | 'bar' | 'area') => void;
}

interface AggregatedData {
  period: string;
  totalTime: number;
  sessions: number;
  averageRating: number;
  date: Date;
}

export const TimeAggregationView: React.FC<TimeAggregationViewProps> = ({
  sessions,
  dateRange,
  aggregationType,
  chartType = 'line',
  onAggregationChange,
  onChartTypeChange,
}) => {
  const theme = useDynamicTheme();

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    title: {
      ...ExpressiveTypography.titleMedium,
      color: theme.colors.onSurface,
    },
    controls: {
      flexDirection: 'row',
      gap: 8,
    },
    controlGroup: {
      flexDirection: 'row',
      backgroundColor: theme.colors.surfaceContainer,
      borderRadius: 16,
      padding: 2,
    },
    controlButton: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 14,
      minWidth: 50,
      alignItems: 'center',
    },
    activeControlButton: {
      backgroundColor: theme.colors.primary,
    },
    controlButtonText: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurfaceVariant,
    },
    activeControlButtonText: {
      color: theme.colors.onPrimary,
      fontWeight: '600',
    },
    chartContainer: {
      alignItems: 'center',
      paddingVertical: 16,
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingHorizontal: 16,
      paddingVertical: 16,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outline,
      backgroundColor: theme.colors.surfaceContainer,
    },
    statItem: {
      alignItems: 'center',
    },
    statValue: {
      ...ExpressiveTypography.titleMedium,
      color: theme.colors.primary,
      fontWeight: '600',
    },
    statLabel: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurfaceVariant,
      marginTop: 2,
    },
    emptyState: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 48,
      paddingHorizontal: 32,
    },
    emptyText: {
      ...ExpressiveTypography.bodyLarge,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
    },
  });

  // Aggregate data based on the selected type
  const aggregatedData = useMemo((): AggregatedData[] => {
    if (sessions.length === 0) return [];

    const dataMap = new Map<string, {
      totalTime: number;
      sessions: number;
      ratings: number[];
      date: Date;
    }>();

    sessions.forEach(session => {
      const sessionDate = new Date(session.date);
      let periodKey: string;
      let periodDate: Date;

      switch (aggregationType) {
        case 'daily':
          periodKey = sessionDate.toISOString().split('T')[0];
          periodDate = new Date(sessionDate.getFullYear(), sessionDate.getMonth(), sessionDate.getDate());
          break;
        case 'weekly':
          // Get the start of the week (Monday)
          const weekStart = new Date(sessionDate);
          const day = weekStart.getDay();
          const diff = weekStart.getDate() - day + (day === 0 ? -6 : 1);
          weekStart.setDate(diff);
          weekStart.setHours(0, 0, 0, 0);
          periodKey = `week-${weekStart.toISOString().split('T')[0]}`;
          periodDate = weekStart;
          break;
        case 'monthly':
          periodKey = `${sessionDate.getFullYear()}-${sessionDate.getMonth() + 1}`;
          periodDate = new Date(sessionDate.getFullYear(), sessionDate.getMonth(), 1);
          break;
        default:
          periodKey = sessionDate.toISOString().split('T')[0];
          periodDate = sessionDate;
      }

      if (!dataMap.has(periodKey)) {
        dataMap.set(periodKey, {
          totalTime: 0,
          sessions: 0,
          ratings: [],
          date: periodDate,
        });
      }

      const data = dataMap.get(periodKey)!;
      data.totalTime += session.duration;
      data.sessions += 1;
      if (session.productivity_rating !== null) {
        data.ratings.push(session.productivity_rating);
      }
    });

    return Array.from(dataMap.entries())
      .map(([period, data]) => ({
        period: formatPeriodLabel(data.date, aggregationType),
        totalTime: data.totalTime,
        sessions: data.sessions,
        averageRating: data.ratings.length > 0 
          ? data.ratings.reduce((sum, rating) => sum + rating, 0) / data.ratings.length 
          : 0,
        date: data.date,
      }))
      .sort((a, b) => a.date.getTime() - b.date.getTime());
  }, [sessions, aggregationType]);

  // Format period label
  const formatPeriodLabel = (date: Date, type: 'daily' | 'weekly' | 'monthly'): string => {
    switch (type) {
      case 'daily':
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      case 'weekly':
        const weekEnd = new Date(date);
        weekEnd.setDate(weekEnd.getDate() + 6);
        return `${date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`;
      case 'monthly':
        return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
      default:
        return date.toLocaleDateString();
    }
  };

  // Format time duration
  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  // Prepare chart data
  const chartData = useMemo(() => {
    return aggregatedData.map((item, index) => ({
      x: index + 1,
      y: item.totalTime / 3600, // Convert to hours for better readability
      label: `${formatDuration(item.totalTime)}\n${item.sessions} sessions`,
      period: item.period,
    }));
  }, [aggregatedData]);

  // Calculate statistics
  const stats = useMemo(() => {
    if (aggregatedData.length === 0) {
      return {
        totalTime: 0,
        averageTime: 0,
        totalSessions: 0,
        averageRating: 0,
      };
    }

    const totalTime = aggregatedData.reduce((sum, item) => sum + item.totalTime, 0);
    const totalSessions = aggregatedData.reduce((sum, item) => sum + item.sessions, 0);
    const ratingsSum = aggregatedData.reduce((sum, item) => sum + (item.averageRating * item.sessions), 0);
    const averageTime = totalTime / aggregatedData.length;
    const averageRating = totalSessions > 0 ? ratingsSum / totalSessions : 0;

    return {
      totalTime,
      averageTime,
      totalSessions,
      averageRating,
    };
  }, [aggregatedData]);

  // Render chart
  const renderChart = () => {
    if (chartData.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Text style={styles.emptyText}>
            No data available for the selected time period and aggregation.
          </Text>
        </View>
      );
    }

    const ChartComponent = chartType === 'bar' ? VictoryBar : chartType === 'area' ? VictoryArea : VictoryLine;

    return (
      <VictoryChart
        theme={VictoryTheme.material}
        width={screenWidth - 32}
        height={280}
        padding={{ left: 60, top: 20, right: 40, bottom: 60 }}
        containerComponent={<VictoryContainer responsive={false} />}
      >
        <VictoryAxis
          dependentAxis
          tickFormat={(t) => `${t}h`}
          style={{
            tickLabels: {
              fontSize: 10,
              fill: theme.colors.onSurfaceVariant,
            },
            grid: {
              stroke: theme.colors.outline,
              strokeWidth: 0.5,
            },
          }}
        />
        <VictoryAxis
          tickFormat={(t) => {
            const dataPoint = aggregatedData[t - 1];
            return dataPoint ? dataPoint.period.split(' ')[0] : '';
          }}
          style={{
            tickLabels: {
              fontSize: 10,
              fill: theme.colors.onSurfaceVariant,
              angle: -45,
            },
          }}
        />
        <ChartComponent
          data={chartData}
          style={{
            data: {
              fill: chartType === 'area' ? theme.colors.primaryContainer : undefined,
              fillOpacity: chartType === 'area' ? 0.3 : undefined,
              stroke: theme.colors.primary,
              strokeWidth: chartType === 'line' ? 3 : undefined,
            },
          }}
          labelComponent={<VictoryTooltip />}
          animate={{
            duration: 1000,
            onLoad: { duration: 500 },
          }}
        />
      </VictoryChart>
    );
  };

  return (
    <ExpressiveCard style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Time Analysis</Text>
        <View style={styles.controls}>
          {/* Aggregation Type */}
          <View style={styles.controlGroup}>
            {(['daily', 'weekly', 'monthly'] as const).map((type) => (
              <TouchableOpacity
                key={type}
                style={[
                  styles.controlButton,
                  aggregationType === type && styles.activeControlButton,
                ]}
                onPress={() => onAggregationChange?.(type)}
              >
                <Text
                  style={[
                    styles.controlButtonText,
                    aggregationType === type && styles.activeControlButtonText,
                  ]}
                >
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Chart Type */}
          <View style={styles.controlGroup}>
            {(['line', 'bar', 'area'] as const).map((type) => (
              <TouchableOpacity
                key={type}
                style={[
                  styles.controlButton,
                  chartType === type && styles.activeControlButton,
                ]}
                onPress={() => onChartTypeChange?.(type)}
              >
                <Text
                  style={[
                    styles.controlButtonText,
                    chartType === type && styles.activeControlButtonText,
                  ]}
                >
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>

      {/* Chart */}
      <View style={styles.chartContainer}>
        {renderChart()}
      </View>

      {/* Statistics */}
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{formatDuration(stats.totalTime)}</Text>
          <Text style={styles.statLabel}>Total Time</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{formatDuration(stats.averageTime)}</Text>
          <Text style={styles.statLabel}>Average</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{stats.totalSessions}</Text>
          <Text style={styles.statLabel}>Sessions</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{stats.averageRating.toFixed(1)}⭐</Text>
          <Text style={styles.statLabel}>Rating</Text>
        </View>
      </View>
    </ExpressiveCard>
  );
};

export default TimeAggregationView;
