import { supabase } from './client';
import { Database } from '../../types/database';

type Group = Database['public']['Tables']['groups']['Row'];
type GroupInsert = Database['public']['Tables']['groups']['Insert'];
type GroupUpdate = Database['public']['Tables']['groups']['Update'];

export interface CreateGroupData {
  name: string;
  description?: string;
  is_public?: boolean;
}

export interface UpdateGroupData {
  name?: string;
  description?: string;
  is_public?: boolean;
}

export interface GroupMemberInfo {
  id: string;
  email: string;
  full_name: string | null;
  avatar_url: string | null;
  role: 'owner' | 'admin' | 'member';
  joined_at: string;
}

// ===========================================
// 🏢 GROUP CRUD OPERATIONS
// ===========================================

/**
 * Create a new group
 */
export const createGroup = async (
  userId: string,
  groupData: CreateGroupData
): Promise<Group> => {
  try {
    const inviteCode = generateInviteCode();
    
    const { data, error } = await supabase
      .from('groups')
      .insert({
        name: groupData.name,
        description: groupData.description || null,
        created_by: userId,
        members: [userId],
        is_public: groupData.is_public || false,
        invite_code: inviteCode,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating group:', error);
    throw error;
  }
};

/**
 * Get all groups for a user
 */
export const getUserGroups = async (userId: string): Promise<Group[]> => {
  try {
    const { data, error } = await supabase
      .from('groups')
      .select('*')
      .or(`created_by.eq.${userId},members.cs.{${userId}}`)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching user groups:', error);
    throw error;
  }
};

/**
 * Get a specific group by ID
 */
export const getGroupById = async (groupId: string): Promise<Group | null> => {
  try {
    const { data, error } = await supabase
      .from('groups')
      .select('*')
      .eq('id', groupId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error('Error fetching group:', error);
    throw error;
  }
};

/**
 * Update a group
 */
export const updateGroup = async (
  groupId: string,
  updates: UpdateGroupData
): Promise<Group> => {
  try {
    const { data, error } = await supabase
      .from('groups')
      .update(updates)
      .eq('id', groupId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating group:', error);
    throw error;
  }
};

/**
 * Delete a group
 */
export const deleteGroup = async (groupId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('groups')
      .delete()
      .eq('id', groupId);

    if (error) throw error;
  } catch (error) {
    console.error('Error deleting group:', error);
    throw error;
  }
};

// ===========================================
// 👥 MEMBER MANAGEMENT
// ===========================================

/**
 * Get group members with their profile information
 */
export const getGroupMembers = async (groupId: string): Promise<GroupMemberInfo[]> => {
  try {
    const group = await getGroupById(groupId);
    if (!group) throw new Error('Group not found');

    const { data, error } = await supabase
      .from('profiles')
      .select('id, email, full_name, avatar_url')
      .in('id', group.members);

    if (error) throw error;

    const members: GroupMemberInfo[] = data.map(profile => ({
      ...profile,
      role: profile.id === group.created_by ? 'owner' : 'member',
      joined_at: new Date().toISOString(), // In a real app, you'd track this
    }));

    return members;
  } catch (error) {
    console.error('Error fetching group members:', error);
    throw error;
  }
};

/**
 * Add a member to a group
 */
export const addGroupMember = async (
  groupId: string,
  userId: string
): Promise<Group> => {
  try {
    const group = await getGroupById(groupId);
    if (!group) throw new Error('Group not found');

    if (group.members.includes(userId)) {
      throw new Error('User is already a member of this group');
    }

    const updatedMembers = [...group.members, userId];

    const { data, error } = await supabase
      .from('groups')
      .update({ members: updatedMembers })
      .eq('id', groupId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error adding group member:', error);
    throw error;
  }
};

/**
 * Remove a member from a group
 */
export const removeGroupMember = async (
  groupId: string,
  userId: string
): Promise<Group> => {
  try {
    const group = await getGroupById(groupId);
    if (!group) throw new Error('Group not found');

    if (group.created_by === userId) {
      throw new Error('Cannot remove the group owner');
    }

    const updatedMembers = group.members.filter(id => id !== userId);

    const { data, error } = await supabase
      .from('groups')
      .update({ members: updatedMembers })
      .eq('id', groupId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error removing group member:', error);
    throw error;
  }
};

/**
 * Leave a group (for non-owners)
 */
export const leaveGroup = async (groupId: string, userId: string): Promise<void> => {
  try {
    const group = await getGroupById(groupId);
    if (!group) throw new Error('Group not found');

    if (group.created_by === userId) {
      throw new Error('Group owner cannot leave. Transfer ownership or delete the group.');
    }

    await removeGroupMember(groupId, userId);
  } catch (error) {
    console.error('Error leaving group:', error);
    throw error;
  }
};

// ===========================================
// 🔗 INVITE CODE MANAGEMENT
// ===========================================

/**
 * Generate a unique invite code
 */
export const generateInviteCode = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

/**
 * Regenerate invite code for a group
 */
export const regenerateInviteCode = async (groupId: string): Promise<string> => {
  try {
    const newInviteCode = generateInviteCode();
    
    await updateGroup(groupId, { invite_code: newInviteCode });
    return newInviteCode;
  } catch (error) {
    console.error('Error regenerating invite code:', error);
    throw error;
  }
};

/**
 * Join a group using invite code
 */
export const joinGroupByInviteCode = async (
  inviteCode: string,
  userId: string
): Promise<Group> => {
  try {
    const { data: groups, error } = await supabase
      .from('groups')
      .select('*')
      .eq('invite_code', inviteCode);

    if (error) throw error;
    if (!groups || groups.length === 0) {
      throw new Error('Invalid invite code');
    }

    const group = groups[0];
    
    if (group.members.includes(userId)) {
      throw new Error('You are already a member of this group');
    }

    return await addGroupMember(group.id, userId);
  } catch (error) {
    console.error('Error joining group by invite code:', error);
    throw error;
  }
};

/**
 * Disable invite code for a group
 */
export const disableInviteCode = async (groupId: string): Promise<void> => {
  try {
    await updateGroup(groupId, { invite_code: null });
  } catch (error) {
    console.error('Error disabling invite code:', error);
    throw error;
  }
};

// ===========================================
// 🔍 SEARCH AND DISCOVERY
// ===========================================

/**
 * Search for public groups
 */
export const searchPublicGroups = async (query: string): Promise<Group[]> => {
  try {
    const { data, error } = await supabase
      .from('groups')
      .select('*')
      .eq('is_public', true)
      .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
      .order('created_at', { ascending: false })
      .limit(20);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error searching public groups:', error);
    throw error;
  }
};

/**
 * Get group statistics
 */
export const getGroupStats = async (groupId: string) => {
  try {
    const group = await getGroupById(groupId);
    if (!group) throw new Error('Group not found');

    // Get member count
    const memberCount = group.members.length;

    // Get total study sessions for the group (if you have this data)
    // This would require a more complex query joining with study_sessions
    
    // Get group creation date
    const createdAt = new Date(group.created_at);
    const daysActive = Math.floor((Date.now() - createdAt.getTime()) / (1000 * 60 * 60 * 24));

    return {
      memberCount,
      daysActive,
      isPublic: group.is_public,
      hasInviteCode: !!group.invite_code,
    };
  } catch (error) {
    console.error('Error getting group stats:', error);
    throw error;
  }
};

// ===========================================
// 🔄 REAL-TIME HELPERS
// ===========================================

/**
 * Subscribe to group changes
 */
export const subscribeToGroupChanges = (
  groupId: string,
  callback: (payload: any) => void
) => {
  return supabase
    .channel(`group-${groupId}`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'groups',
        filter: `id=eq.${groupId}`,
      },
      callback
    )
    .subscribe();
};

/**
 * Subscribe to group member activity
 */
export const subscribeToGroupActivity = (
  groupId: string,
  callback: (payload: any) => void
) => {
  // This would subscribe to a group_activities table if you have one
  // For now, it's a placeholder for future implementation
  return supabase
    .channel(`group-activity-${groupId}`)
    .on('broadcast', { event: 'activity' }, callback)
    .subscribe();
};
