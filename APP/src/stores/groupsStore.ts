import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { supabase } from '../services/supabase/client';
import { Database } from '../types/database';

type Group = Database['public']['Tables']['groups']['Row'];
type GroupInsert = Database['public']['Tables']['groups']['Insert'];
type GroupUpdate = Database['public']['Tables']['groups']['Update'];

interface GroupMember {
  id: string;
  email: string;
  full_name: string | null;
  avatar_url: string | null;
  role: 'owner' | 'admin' | 'member';
  joined_at: string;
  is_online: boolean;
  last_activity: string | null;
}

interface GroupInvitation {
  id: string;
  group_id: string;
  invited_by: string;
  invited_email: string;
  invite_code: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  created_at: string;
  expires_at: string;
}

interface GroupActivity {
  id: string;
  group_id: string;
  user_id: string;
  activity_type: 'joined' | 'left' | 'timer_started' | 'timer_paused' | 'timer_completed' | 'task_created' | 'task_updated';
  activity_data: any;
  created_at: string;
}

interface GroupsState {
  // Groups data
  groups: Group[];
  currentGroup: Group | null;
  groupMembers: Record<string, GroupMember[]>;
  groupInvitations: Record<string, GroupInvitation[]>;
  groupActivities: Record<string, GroupActivity[]>;
  
  // UI state
  loading: boolean;
  error: string | null;
  
  // Real-time state
  onlineMembers: Record<string, string[]>; // groupId -> userIds
  realtimeSubscriptions: Record<string, any>;
  
  // Actions
  fetchGroups: (userId: string) => Promise<void>;
  createGroup: (group: Omit<GroupInsert, 'id' | 'created_at' | 'updated_at'>) => Promise<Group>;
  updateGroup: (groupId: string, updates: GroupUpdate) => Promise<void>;
  deleteGroup: (groupId: string) => Promise<void>;
  
  // Member management
  fetchGroupMembers: (groupId: string) => Promise<void>;
  addMember: (groupId: string, userId: string, role?: 'member' | 'admin') => Promise<void>;
  removeMember: (groupId: string, userId: string) => Promise<void>;
  updateMemberRole: (groupId: string, userId: string, role: 'member' | 'admin') => Promise<void>;
  
  // Invitations
  createInvitation: (groupId: string, email: string) => Promise<string>; // Returns invite code
  acceptInvitation: (inviteCode: string) => Promise<void>;
  declineInvitation: (inviteCode: string) => Promise<void>;
  fetchGroupInvitations: (groupId: string) => Promise<void>;
  
  // Real-time collaboration
  joinGroup: (groupId: string) => Promise<void>;
  leaveGroup: (groupId: string) => Promise<void>;
  updateMemberActivity: (groupId: string, activityType: GroupActivity['activity_type'], data?: any) => Promise<void>;
  
  // Utility
  setCurrentGroup: (group: Group | null) => void;
  generateInviteCode: () => string;
  reset: () => void;
}

const initialState = {
  groups: [],
  currentGroup: null,
  groupMembers: {},
  groupInvitations: {},
  groupActivities: {},
  loading: false,
  error: null,
  onlineMembers: {},
  realtimeSubscriptions: {},
};

export const useGroupsStore = create<GroupsState>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,

    fetchGroups: async (userId: string) => {
      set({ loading: true, error: null });
      
      try {
        const { data, error } = await supabase
          .from('groups')
          .select('*')
          .or(`created_by.eq.${userId},members.cs.{${userId}}`);

        if (error) throw error;

        set({ groups: data || [], loading: false });
      } catch (error) {
        console.error('Error fetching groups:', error);
        set({ error: (error as Error).message, loading: false });
      }
    },

    createGroup: async (groupData) => {
      set({ loading: true, error: null });
      
      try {
        const inviteCode = get().generateInviteCode();
        
        const { data, error } = await supabase
          .from('groups')
          .insert({
            ...groupData,
            members: [groupData.created_by],
            invite_code: inviteCode,
          })
          .select()
          .single();

        if (error) throw error;

        set(state => ({
          groups: [...state.groups, data],
          loading: false,
        }));

        return data;
      } catch (error) {
        console.error('Error creating group:', error);
        set({ error: (error as Error).message, loading: false });
        throw error;
      }
    },

    updateGroup: async (groupId: string, updates: GroupUpdate) => {
      set({ loading: true, error: null });
      
      try {
        const { data, error } = await supabase
          .from('groups')
          .update(updates)
          .eq('id', groupId)
          .select()
          .single();

        if (error) throw error;

        set(state => ({
          groups: state.groups.map(group => 
            group.id === groupId ? data : group
          ),
          currentGroup: state.currentGroup?.id === groupId ? data : state.currentGroup,
          loading: false,
        }));
      } catch (error) {
        console.error('Error updating group:', error);
        set({ error: (error as Error).message, loading: false });
      }
    },

    deleteGroup: async (groupId: string) => {
      set({ loading: true, error: null });
      
      try {
        const { error } = await supabase
          .from('groups')
          .delete()
          .eq('id', groupId);

        if (error) throw error;

        set(state => ({
          groups: state.groups.filter(group => group.id !== groupId),
          currentGroup: state.currentGroup?.id === groupId ? null : state.currentGroup,
          loading: false,
        }));
      } catch (error) {
        console.error('Error deleting group:', error);
        set({ error: (error as Error).message, loading: false });
      }
    },

    fetchGroupMembers: async (groupId: string) => {
      try {
        const group = get().groups.find(g => g.id === groupId);
        if (!group) return;

        const { data, error } = await supabase
          .from('profiles')
          .select('id, email, full_name, avatar_url')
          .in('id', group.members);

        if (error) throw error;

        const members: GroupMember[] = data.map(profile => ({
          ...profile,
          role: profile.id === group.created_by ? 'owner' : 'member',
          joined_at: new Date().toISOString(),
          is_online: false,
          last_activity: null,
        }));

        set(state => ({
          groupMembers: {
            ...state.groupMembers,
            [groupId]: members,
          },
        }));
      } catch (error) {
        console.error('Error fetching group members:', error);
        set({ error: (error as Error).message });
      }
    },

    addMember: async (groupId: string, userId: string, role = 'member') => {
      try {
        const group = get().groups.find(g => g.id === groupId);
        if (!group) throw new Error('Group not found');

        const updatedMembers = [...group.members, userId];

        await get().updateGroup(groupId, { members: updatedMembers });
        await get().fetchGroupMembers(groupId);
      } catch (error) {
        console.error('Error adding member:', error);
        set({ error: (error as Error).message });
      }
    },

    removeMember: async (groupId: string, userId: string) => {
      try {
        const group = get().groups.find(g => g.id === groupId);
        if (!group) throw new Error('Group not found');

        const updatedMembers = group.members.filter(id => id !== userId);

        await get().updateGroup(groupId, { members: updatedMembers });
        await get().fetchGroupMembers(groupId);
      } catch (error) {
        console.error('Error removing member:', error);
        set({ error: (error as Error).message });
      }
    },

    updateMemberRole: async (groupId: string, userId: string, role: 'member' | 'admin') => {
      try {
        // Update member role in local state
        set(state => ({
          groupMembers: {
            ...state.groupMembers,
            [groupId]: state.groupMembers[groupId]?.map(member =>
              member.id === userId ? { ...member, role } : member
            ) || [],
          },
        }));
      } catch (error) {
        console.error('Error updating member role:', error);
        set({ error: (error as Error).message });
      }
    },

    createInvitation: async (groupId: string, email: string) => {
      try {
        const inviteCode = get().generateInviteCode();
        
        // In a real implementation, you'd create an invitation record
        // For now, we'll just return the invite code
        return inviteCode;
      } catch (error) {
        console.error('Error creating invitation:', error);
        set({ error: (error as Error).message });
        throw error;
      }
    },

    acceptInvitation: async (inviteCode: string) => {
      try {
        // Find group by invite code and add current user
        const { data: groups, error } = await supabase
          .from('groups')
          .select('*')
          .eq('invite_code', inviteCode);

        if (error) throw error;
        if (!groups || groups.length === 0) throw new Error('Invalid invite code');

        const group = groups[0];
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) throw new Error('User not authenticated');

        await get().addMember(group.id, user.id);
      } catch (error) {
        console.error('Error accepting invitation:', error);
        set({ error: (error as Error).message });
        throw error;
      }
    },

    declineInvitation: async (inviteCode: string) => {
      try {
        // In a real implementation, you'd update the invitation status
        console.log('Invitation declined:', inviteCode);
      } catch (error) {
        console.error('Error declining invitation:', error);
        set({ error: (error as Error).message });
      }
    },

    fetchGroupInvitations: async (groupId: string) => {
      try {
        // In a real implementation, you'd fetch invitations from the database
        set(state => ({
          groupInvitations: {
            ...state.groupInvitations,
            [groupId]: [],
          },
        }));
      } catch (error) {
        console.error('Error fetching group invitations:', error);
        set({ error: (error as Error).message });
      }
    },

    joinGroup: async (groupId: string) => {
      try {
        const group = get().groups.find(g => g.id === groupId);
        if (!group) throw new Error('Group not found');

        set({ currentGroup: group });
        await get().fetchGroupMembers(groupId);
        
        // Set up real-time subscriptions
        // In a real implementation, you'd set up Supabase real-time subscriptions
      } catch (error) {
        console.error('Error joining group:', error);
        set({ error: (error as Error).message });
      }
    },

    leaveGroup: async (groupId: string) => {
      try {
        set(state => ({
          currentGroup: state.currentGroup?.id === groupId ? null : state.currentGroup,
        }));
        
        // Clean up real-time subscriptions
        const subscriptions = get().realtimeSubscriptions;
        if (subscriptions[groupId]) {
          subscriptions[groupId].unsubscribe();
          delete subscriptions[groupId];
        }
      } catch (error) {
        console.error('Error leaving group:', error);
        set({ error: (error as Error).message });
      }
    },

    updateMemberActivity: async (groupId: string, activityType: GroupActivity['activity_type'], data?: any) => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        const activity: Omit<GroupActivity, 'id'> = {
          group_id: groupId,
          user_id: user.id,
          activity_type: activityType,
          activity_data: data || {},
          created_at: new Date().toISOString(),
        };

        // In a real implementation, you'd save this to the database
        set(state => ({
          groupActivities: {
            ...state.groupActivities,
            [groupId]: [
              ...(state.groupActivities[groupId] || []),
              { ...activity, id: Date.now().toString() },
            ].slice(-50), // Keep only last 50 activities
          },
        }));
      } catch (error) {
        console.error('Error updating member activity:', error);
        set({ error: (error as Error).message });
      }
    },

    setCurrentGroup: (group: Group | null) => {
      set({ currentGroup: group });
    },

    generateInviteCode: () => {
      return Math.random().toString(36).substring(2, 15) + 
             Math.random().toString(36).substring(2, 15);
    },

    reset: () => {
      set(initialState);
    },
  }))
);
